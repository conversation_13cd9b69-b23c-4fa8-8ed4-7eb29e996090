"""
Utility functions for handling speaker diarization data.
"""

import asyncio
import logging
from typing import Dict, List, Optional

from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.llm.llm_providers.llm_helpers import LLMProviderError
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)


async def classify_speaker(
    llm_orchestrator: LLMOrchestrator,
    speaker_text: str,
    speaker_id: str = "unknown",
    use_cache: bool = True,
    response_usage: Optional[ResponseUsage] = None,
) -> str:
    """
    Classify a speaker based on their speech content using LLM.

    Args:
        llm_orchestrator: The LLM orchestrator instance to use for classification
        speaker_text: The text spoken by the speaker to analyze
        speaker_id: Optional speaker identifier for context
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs

    Returns:
        Classification result: "doctor", "patient", or "other"

    Raises:
        Exception: If LLM call fails or returns invalid classification
    """
    if not speaker_text or not speaker_text.strip():
        logger.warning("Empty speaker text provided for speaker %s", speaker_id)
        return "other"

    # Create a detailed prompt for speaker classification
    prompt = f"""
You are an expert medical conversation analyst. Your task is to classify speakers in medical conversations based on their speech patterns, vocabulary, and content.

Analyze the following text spoken by a speaker and classify them into one of these three categories:

1. **doctor** - Medical professionals (doctors, nurses, specialists, medical staff)
   - Uses medical terminology and jargon
   - Asks diagnostic questions
   - Provides medical advice or explanations
   - Discusses treatment plans, medications, or procedures
   - Shows professional medical knowledge

2. **patient** - Patients or their representatives
   - Describes symptoms, pain, or health concerns
   - Asks questions about their condition
   - Expresses worry, confusion, or seeks reassurance
   - Uses lay terminology for medical issues
   - Discusses personal health experiences

3. **other** - Anyone else (family members, administrative staff, etc.)
   - Does not fit the doctor or patient categories
   - May be administrative, supportive, or unrelated to medical care

Speaker ID: {speaker_id}
Speaker Text: "{speaker_text}"

Based on the content, vocabulary, and context of this speech, classify this speaker.

Respond with ONLY one word: "doctor", "patient", or "other"
"""

    try:
        # Call the LLM with the classification prompt
        response = await llm_orchestrator.call_llm(
            prompt=prompt,
            max_tokens=10,  # We only need a single word response
            use_cache=use_cache,
            response_usage=response_usage,
            temperature=0.1,  # Low temperature for consistent classification
        )

        # Clean and validate the response
        classification = response.strip().lower()

        # Ensure the response is one of the valid classifications
        valid_classifications = {"doctor", "patient", "other"}
        if classification not in valid_classifications:
            logger.warning(
                "LLM returned invalid classification '%s' for speaker %s. "
                "Expected one of: %s. Defaulting to 'other'.",
                classification,
                speaker_id,
                valid_classifications,
            )
            return "other"

        logger.info("Speaker %s classified as: %s", speaker_id, classification)
        return classification

    except LLMProviderError as e:
        logger.error(
            "LLM provider failed to classify speaker %s: %s", speaker_id, str(e)
        )
        # Return "other" as a safe default if classification fails
        return "other"
    except Exception as e:
        logger.error("Unexpected error classifying speaker %s: %s", speaker_id, str(e))
        # Return "other" as a safe default if classification fails
        return "other"


async def classify_speakers_from_visit_report(
    task_id: str,
    speakers_data: Optional[Dict[str, List[str]]],
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
) -> Optional[Dict[str, str]]:
    """
    Classify speakers from visit report speaker data in parallel.

    Args:
        task_id: Task identifier for logging
        speakers_data: Dictionary mapping speaker IDs to lists of their sentences
        llm_orchestrator: LLM orchestrator for classification
        response_usage: Optional ResponseUsage object to track costs

    Returns:
        Dictionary mapping speaker IDs to classifications or None if no speaker data
    """
    if not speakers_data:
        logger.info("No speaker data available for classification in task %s", task_id)
        return None

    logger.info("Starting speaker classification for task %s", task_id)

    # Create classification tasks for parallel execution
    classification_tasks = []
    speaker_ids = []

    for speaker_id, sentences in speakers_data.items():
        if not sentences:
            logger.warning(
                "No sentences found for speaker %s in task %s", speaker_id, task_id
            )
            continue

        # Combine all sentences for this speaker to get more context
        combined_text = " ".join(sentences)

        # Create classification task
        task = classify_speaker(
            llm_orchestrator=llm_orchestrator,
            speaker_text=combined_text,
            speaker_id=speaker_id,
            use_cache=True,
            response_usage=response_usage,
        )
        classification_tasks.append(task)
        speaker_ids.append(speaker_id)

    if not classification_tasks:
        logger.warning("No valid speakers found for classification in task %s", task_id)
        return {}

    # Run all classifications in parallel
    logger.info(
        "Classifying %d speakers in parallel for task %s",
        len(classification_tasks),
        task_id,
    )
    results = await asyncio.gather(*classification_tasks, return_exceptions=True)

    # Build the classification dictionary
    speaker_classifications = {}
    for i, speaker_id in enumerate(speaker_ids):
        result = results[i]
        if isinstance(result, Exception):
            logger.error(
                "Failed to classify speaker %s in task %s: %s",
                speaker_id,
                task_id,
                str(result),
            )
            speaker_classifications[speaker_id] = "other"  # Default fallback
        else:
            speaker_classifications[speaker_id] = result

    logger.info(
        "Speaker classification completed for task %s: %s",
        task_id,
        speaker_classifications,
    )
    return speaker_classifications
