"""
Utility functions for handling speaker diarization data.
"""

import logging
from typing import Dict, List, Optional

from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.llm.llm_providers.llm_helpers import LLMProviderError
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)


async def classify_speaker(
    llm_orchestrator: LLMOrchestrator,
    speaker_text: str,
    speaker_id: str = "unknown",
    use_cache: bool = True,
    response_usage: Optional[ResponseUsage] = None,
) -> str:
    """
    Classify a speaker based on their speech content using LLM.

    Args:
        llm_orchestrator: The LLM orchestrator instance to use for classification
        speaker_text: The text spoken by the speaker to analyze
        speaker_id: Optional speaker identifier for context
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs

    Returns:
        Classification result: "doctor", "patient", or "other"

    Raises:
        Exception: If LLM call fails or returns invalid classification
    """
    if not speaker_text or not speaker_text.strip():
        logger.warning("Empty speaker text provided for speaker %s", speaker_id)
        return "other"

    # Create a detailed prompt for speaker classification
    prompt = f"""
You are an expert medical conversation analyst. Your task is to classify speakers in medical conversations based on their speech patterns, vocabulary, and content.

Analyze the following text spoken by a speaker and classify them into one of these three categories:

1. **doctor** - Medical professionals (doctors, nurses, specialists, medical staff)
   - Uses medical terminology and jargon
   - Asks diagnostic questions
   - Provides medical advice or explanations
   - Discusses treatment plans, medications, or procedures
   - Shows professional medical knowledge

2. **patient** - Patients or their representatives
   - Describes symptoms, pain, or health concerns
   - Asks questions about their condition
   - Expresses worry, confusion, or seeks reassurance
   - Uses lay terminology for medical issues
   - Discusses personal health experiences

3. **other** - Anyone else (family members, administrative staff, etc.)
   - Does not fit the doctor or patient categories
   - May be administrative, supportive, or unrelated to medical care

Speaker ID: {speaker_id}
Speaker Text: "{speaker_text}"

Based on the content, vocabulary, and context of this speech, classify this speaker.

Respond with ONLY one word: "doctor", "patient", or "other"
"""

    try:
        # Call the LLM with the classification prompt
        response = await llm_orchestrator.call_llm(
            prompt=prompt,
            max_tokens=10,  # We only need a single word response
            use_cache=use_cache,
            response_usage=response_usage,
            temperature=0.1,  # Low temperature for consistent classification
        )

        # Clean and validate the response
        classification = response.strip().lower()

        # Ensure the response is one of the valid classifications
        valid_classifications = {"doctor", "patient", "other"}
        if classification not in valid_classifications:
            logger.warning(
                "LLM returned invalid classification '%s' for speaker %s. "
                "Expected one of: %s. Defaulting to 'other'.",
                classification,
                speaker_id,
                valid_classifications,
            )
            return "other"

        logger.info("Speaker %s classified as: %s", speaker_id, classification)
        return classification

    except LLMProviderError as e:
        logger.error(
            "LLM provider failed to classify speaker %s: %s", speaker_id, str(e)
        )
        # Return "other" as a safe default if classification fails
        return "other"
    except Exception as e:
        logger.error("Unexpected error classifying speaker %s: %s", speaker_id, str(e))
        # Return "other" as a safe default if classification fails
        return "other"
