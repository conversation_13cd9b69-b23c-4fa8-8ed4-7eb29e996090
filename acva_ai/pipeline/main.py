import asyncio
import io
import logging
import uuid
from typing import Dict, List, Optional

from fastapi import UploadFile
from pydub import AudioSegment

from acva_ai._params import DEFAULT_LLM_PROVIDER, DEFAULT_TRANSCRIPT_PROVIDER
from acva_ai.database import mongo_instance
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.llm.llm_providers.llm_provider import LLMProvider
from acva_ai.llm.scenarios.grammar import grammar_check_by_chunks
from acva_ai.llm.scenarios.medical_report import build_medical_report_with_pipeline
from acva_ai.llm.transcript_orchestrator import TranscriptOrchestrator
from acva_ai.llm.transcript_providers.transcript_provider import TranscriptProvider
from acva_ai.models.models import Task
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.pipeline.affections_processing import process_affections
from acva_ai.pipeline.audio_save import save_audio_segment
from acva_ai.pipeline.domain_insights import process_domain_insights
from acva_ai.pipeline.html_generation import convert_domain_insights_to_html
from acva_ai.pipeline.medication_processing import process_medication
from acva_ai.pipeline.transcription import generate_transcription
from acva_ai.services.acva_server import send_callback
from acva_ai.utils.audio_utils import concatenate_audio_files, normalize_audio_segment
from acva_ai.utils.speaker_utils import classify_speaker
from acva_ai.utils.usage import ResponseUsage

# Set up logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


async def classify_speakers_from_visit_report(
    task_id: str,
    visit_report: VisitReport,
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
) -> Optional[Dict[str, str]]:
    """
    Classify speakers from visit report speaker data in parallel.

    Args:
        task_id: Task identifier for logging
        visit_report: VisitReport containing speakers_data
        llm_orchestrator: LLM orchestrator for classification
        response_usage: Optional ResponseUsage object to track costs

    Returns:
        Dictionary mapping speaker IDs to classifications or None if no speaker data
    """
    if not visit_report.speakers_data:
        logger.info("No speaker data available for classification in task %s", task_id)
        return None

    logger.info("Starting speaker classification for task %s", task_id)

    # Create tasks for parallel classification
    classification_tasks = []
    for speaker_id, sentences in visit_report.speakers_data.items():
        if not sentences:
            logger.warning(
                "No sentences found for speaker %s in task %s", speaker_id, task_id
            )
            continue

        # Combine all sentences for this speaker to get more context
        combined_text = " ".join(sentences)

        # Create classification task
        task = classify_speaker(
            llm_orchestrator=llm_orchestrator,
            speaker_text=combined_text,
            speaker_id=speaker_id,
            use_cache=True,
            response_usage=response_usage,
        )
        classification_tasks.append((speaker_id, task))

    if not classification_tasks:
        logger.warning("No valid speakers found for classification in task %s", task_id)
        return {}

    # Run all classifications in parallel
    logger.info(
        "Classifying %d speakers in parallel for task %s",
        len(classification_tasks),
        task_id,
    )
    results = await asyncio.gather(
        *[task for _, task in classification_tasks], return_exceptions=True
    )

    # Build the classification dictionary
    classifications = {}
    for i, (speaker_id, _) in enumerate(classification_tasks):
        result = results[i]
        if isinstance(result, Exception):
            logger.error(
                "Failed to classify speaker %s in task %s: %s",
                speaker_id,
                task_id,
                str(result),
            )
            classifications[speaker_id] = "other"  # Default fallback
        else:
            classifications[speaker_id] = result

    logger.info(
        "Speaker classification completed for task %s: %s", task_id, classifications
    )
    return classifications


def create_task(
    llm_orchestrator: LLMOrchestrator,
    transcript_orchestrator: TranscriptOrchestrator,
    task_id: Optional[str] = None,
):
    task_uuid = uuid.UUID(task_id) if task_id else uuid.uuid4()
    task_id = str(task_uuid)

    # Check if task already exists (for requeue scenarios)
    existing_task = mongo_instance.get_task(task_id)
    if existing_task:
        logger.info(f"Task {task_id} already exists, reusing existing task")
        return task_id

    # Create a task entry in the database
    task = Task(
        task_id=task_uuid,
        status="queued",
        metadata={
            "llm_provider": llm_orchestrator.primary_provider,
            "transcript_provider": transcript_orchestrator.primary_provider,
        },
    ).model_dump()

    mongo_instance.create_task(task)

    return task_id


async def process_visit_audios(audio_files: List[UploadFile]):
    audio_segment = concatenate_audio_files(audio_files)
    audio_segment = normalize_audio_segment(audio_segment)

    await process_visit(
        audio_segment=audio_segment,
        llm_provider=DEFAULT_LLM_PROVIDER,
        transcript_provider=DEFAULT_TRANSCRIPT_PROVIDER,
    )


async def process_visit(
    audio_segment: AudioSegment,
    llm_provider: LLMProvider,
    transcript_provider: TranscriptProvider,
    task_id: Optional[str] = None,
    diarize: bool = False,
) -> None:
    """
    Main pipeline for processing medical visit audio with enhanced state tracking
    and error handling

    Args:
        task_id: Unique identifier for the task
        audio_segment: Audio segment to process
        llm_provider: Primary LLM provider to use for all LLM calls
        transcript_provider: Primary transcript provider to use for audio transcription
        diarize: Enable speaker diarization (only supported by ElevenLabs)
    """

    llm_orchestrator = LLMOrchestrator(llm_provider)
    transcript_orchestrator = TranscriptOrchestrator(transcript_provider)

    task_id = create_task(llm_orchestrator, transcript_orchestrator, task_id)

    # Initialize audio_processing status and visit report
    processing_status = ProcessingStatus(
        task_id=task_id, overall_status="started", current_stage="started"
    )
    visit_report = VisitReport(task_id=task_id)
    response_usage = ResponseUsage()

    # Add orchestrator to visit report metadata
    visit_report.metadata = {
        "llm_provider": llm_orchestrator.primary_provider,
        "transcript_provider": transcript_orchestrator.primary_provider,
    }

    # Initialize database state
    mongo_instance.update_processing_status(task_id, processing_status.dict())
    mongo_instance.save_visit_report(task_id, visit_report.dict())

    save_audio_segment(task_id=task_id, audio_segment=audio_segment)

    # Stage 1: Audio Transcription
    await generate_transcription(
        task_id=task_id,
        audio_segment=audio_segment,
        visit_report=visit_report,
        processing_status=processing_status,
        response_usage=response_usage,
        transcript_orchestrator=transcript_orchestrator,
        diarize=diarize,
    )

    # Classify speakers if diarization data is available
    if diarize and visit_report.speakers_data:
        speaker_classifications = await classify_speakers_from_visit_report(
            task_id=task_id,
            visit_report=visit_report,
            llm_orchestrator=llm_orchestrator,
            response_usage=response_usage,
        )

        # Store speaker classifications in visit report metadata
        if speaker_classifications:
            if not visit_report.metadata:
                visit_report.metadata = {}
            visit_report.metadata["speaker_classifications"] = speaker_classifications
            # Update the visit report in database
            mongo_instance.save_visit_report(task_id, visit_report.dict())

    transcript = visit_report.raw_transcript
    if not transcript:
        raise ValueError(
            f"[Task {task_id}] No transcript available for processing. Raw transcript is None or empty."
        )

    await grammar_check_by_chunks(
        task_id=task_id,
        transcript=transcript,
        processing_status=processing_status,
        visit_report=visit_report,
        language="Romanian",
        response_usage=response_usage,
        llm_orchestrator=llm_orchestrator,
    )

    # Run medication, affections, and medical report processing in parallel
    # Get the processed transcript, fallback to raw transcript if needed
    processed_transcript = visit_report.transcript or visit_report.raw_transcript
    if not processed_transcript:
        raise ValueError(
            f"[Task {task_id}] No processed or raw transcript available for downstream processing."
        )

    await asyncio.gather(
        process_medication(
            task_id=task_id,
            processing_status=processing_status,
            visit_report=visit_report,
            response_usage=response_usage,
            llm_orchestrator=llm_orchestrator,
        ),
        process_affections(
            task_id=task_id,
            processing_status=processing_status,
            visit_report=visit_report,
            response_usage=response_usage,
            llm_orchestrator=llm_orchestrator,
        ),
        build_medical_report_with_pipeline(
            task_id=task_id,
            transcript=processed_transcript,
            processing_status=processing_status,
            visit_report=visit_report,
            llm_orchestrator=llm_orchestrator,
            response_usage=response_usage,
        ),
    )

    await process_domain_insights(
        task_id=task_id,
        processing_status=processing_status,
        visit_report=visit_report,
        response_usage=response_usage,
        llm_orchestrator=llm_orchestrator,
    )

    await convert_domain_insights_to_html(
        task_id=task_id,
        processing_status=processing_status,
        visit_report=visit_report,
        response_usage=response_usage,
        llm_orchestrator=llm_orchestrator,
    )

    logger.info(f"[Task {task_id}] Processing completed")
    processing_status.finalize()
    mongo_instance.update_processing_status(task_id, processing_status.dict())

    visit_report.response_usage = response_usage
    visit_report.stage_timings = processing_status.stage_timings
    visit_report.total_processing_time = processing_status.total_processing_time
    mongo_instance.save_visit_report(task_id, visit_report.dict())

    # Send callback with complete report
    logger.info(f"[Task {task_id}] Sending callback")
    callback_result = await send_callback(task_id=task_id, visit_report=visit_report)
    if callback_result:
        logger.info(f"[Task {task_id}] Callback sent successfully")
    else:
        logger.warning(f"[Task {task_id}] Callback failed")


def process_visit_background(
    task_id: str,
    audio_segment: AudioSegment,
    transcript_provider: TranscriptProvider,
    llm_provider: LLMProvider,
    diarize: bool = False,
):
    """
    Synchronous wrapper to run the async process_visit in the background.
    """
    asyncio.run(
        process_visit(
            audio_segment=audio_segment,
            transcript_provider=transcript_provider,
            llm_provider=llm_provider,
            task_id=task_id,
            diarize=diarize,
        )
    )


def test():
    sample_audio_path = ".data/samples/recording-2.mp3"
    sample_audio_path = ".data/demo/1e4cd7fa-b4a1-47ce-99aa-abdf66885b2b.wav"
    task_id = "702814b8-2068-4cc1-b8df-4b0ec09e6243"

    audio_segment = AudioSegment.from_file(sample_audio_path)
    result = asyncio.run(
        process_visit(
            audio_segment=audio_segment,
            transcript_provider=DEFAULT_TRANSCRIPT_PROVIDER,
            llm_provider=DEFAULT_LLM_PROVIDER,
            task_id=task_id,
        )
    )
    print(result)


if __name__ == "__main__":
    test()
